use crate::config::PlaybackConfig;
use tokio::time::{sleep, Duration, Instant};
use tracing::{debug, info};

/// 回放速率控制器
/// 负责控制数据处理的速率，避免过快处理历史数据
#[derive(Debug)]
pub struct RateLimiter {
    /// 回放配置
    config: PlaybackConfig,
    /// 当前秒的开始时间（旧实现遗留，用于统计）
    current_second_start: Option<Instant>,
    /// 当前秒内已处理的数据量（旧实现遗留，用于统计）
    processed_in_current_second: u32,
    /// 下一次允许处理的时间点（用于平均速率控制）
    next_allowed_time: Option<Instant>,
}

impl RateLimiter {
    /// 创建新的速率控制器
    pub fn new(config: PlaybackConfig) -> Self {
        Self {
            config,
            current_second_start: None,
            processed_in_current_second: 0,
            next_allowed_time: None,
        }
    }

    /// 应用速率控制
    /// 平均速率：以固定间隔 1/rate_per_second 间隔发送/处理
    /// 例如 rate_per_second=500 => 每 2ms 一条；10000 => 每 0.1ms 一条
    pub async fn apply_rate_control(&mut self) -> crate::Result<()> {
        if !self.config.enabled || self.config.rate_per_second == 0 {
            // 如果未启用速率控制或速率为0（无限制），直接返回
            debug!(
                "Rate control disabled or unlimited (enabled={}, rate={})",
                self.config.enabled, self.config.rate_per_second
            );
            return Ok(());
        }

        let now = Instant::now();

        // 平均速率控制：使用基于时间的节流，确保事件均匀间隔
        let interval = if self.config.rate_per_second > 0 {
            // 使用浮点转换避免整除丢失精度
            Duration::from_secs_f64(1.0 / self.config.rate_per_second as f64)
        } else {
            Duration::from_secs(0)
        };

        // 首次调用：允许立即通过，并初始化下一次允许时间
        if self.next_allowed_time.is_none() {
            self.next_allowed_time = Some(now);
        }

        let target_time = self.next_allowed_time.expect("next_allowed_time just set");
        if now < target_time {
            let wait_time = target_time - now;
            debug!(
                "🕒 Throttling for average rate: waiting {:?} (interval {:?}, rate={})",
                wait_time, interval, self.config.rate_per_second
            );
            sleep(wait_time).await;
        }

        // 推进下一次允许时间点
        // - 若刚才等待了，则基于target_time推进，保证均匀
        // - 若没有等待（处理落后了），则基于now推进，避免短时突发补发
        let base = if now < target_time {
            target_time
        } else {
            Instant::now()
        };
        self.next_allowed_time = Some(base + interval);

        Ok(())
    }

    /// 记录处理了一个数据项
    pub fn record_processed(&mut self) {
        self.processed_in_current_second += 1;
    }

    /// 批量记录处理的数据项
    pub fn record_batch_processed(&mut self, count: u32) {
        self.processed_in_current_second += count;
    }

    /// 更新配置
    pub fn update_config(&mut self, config: PlaybackConfig) {
        info!(
            "Updating rate limiter config: rate_per_second={}, enabled={}, batch_size={}",
            config.rate_per_second, config.enabled, config.batch_size
        );
        self.config = config;
        // 重置速率控制状态
        self.current_second_start = None;
        self.processed_in_current_second = 0;
        self.next_allowed_time = None;
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &PlaybackConfig {
        &self.config
    }

    /// 获取当前秒内已处理的数据量
    pub fn get_processed_count(&self) -> u32 {
        self.processed_in_current_second
    }

    /// 检查是否启用了速率控制
    pub fn is_enabled(&self) -> bool {
        self.config.enabled && self.config.rate_per_second > 0
    }

    /// 重置速率控制状态
    pub fn reset(&mut self) {
        self.current_second_start = None;
        self.processed_in_current_second = 0;
    }

    /// 获取批处理大小
    pub fn get_batch_size(&self) -> u32 {
        self.config.batch_size
    }

    /// 计算当前速率（每秒处理的数据量）
    pub fn calculate_current_rate(&self) -> f64 {
        if let Some(second_start) = self.current_second_start {
            let elapsed = second_start.elapsed();
            if elapsed.as_secs_f64() > 0.0 {
                self.processed_in_current_second as f64 / elapsed.as_secs_f64()
            } else {
                0.0
            }
        } else {
            0.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::TimeAlignmentConfig;

    #[tokio::test]
    async fn test_rate_limiter_disabled() {
        let config = PlaybackConfig {
            rate_per_second: 100,
            enabled: false,
            batch_size: 10,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        let mut limiter = RateLimiter::new(config);

        // 禁用时应该立即返回
        let start = Instant::now();
        limiter.apply_rate_control().await.unwrap();
        let elapsed = start.elapsed();

        assert!(elapsed.as_millis() < 10); // 应该几乎立即返回
    }

    #[tokio::test]
    async fn test_rate_limiter_unlimited() {
        let config = PlaybackConfig {
            rate_per_second: 0, // 0 表示无限制
            enabled: true,
            batch_size: 10,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        let mut limiter = RateLimiter::new(config);

        // 无限制时应该立即返回
        let start = Instant::now();
        limiter.apply_rate_control().await.unwrap();
        let elapsed = start.elapsed();

        assert!(elapsed.as_millis() < 10); // 应该几乎立即返回
    }

    #[test]
    fn test_record_processed() {
        let config = PlaybackConfig {
            rate_per_second: 100,
            enabled: true,
            batch_size: 10,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        let mut limiter = RateLimiter::new(config);

        assert_eq!(limiter.get_processed_count(), 0);

        limiter.record_processed();
        assert_eq!(limiter.get_processed_count(), 1);

        limiter.record_batch_processed(5);
        assert_eq!(limiter.get_processed_count(), 6);
    }

    #[test]
    fn test_config_update() {
        let initial_config = PlaybackConfig {
            rate_per_second: 100,
            enabled: true,
            batch_size: 10,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        let mut limiter = RateLimiter::new(initial_config);

        let new_config = PlaybackConfig {
            rate_per_second: 200,
            enabled: false,
            batch_size: 20,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        limiter.update_config(new_config.clone());

        assert_eq!(limiter.get_config().rate_per_second, 200);
        assert!(!limiter.get_config().enabled);
        assert_eq!(limiter.get_config().batch_size, 20);
    }
}

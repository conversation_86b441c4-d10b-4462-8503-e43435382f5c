use crate::config::{DataSourceType, DataTypesPaths};
use crate::data::parser::DataParser;
use crate::data::reader::DataReaderStatus;
use crate::types::MarketData;
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use flate2::read::GzDecoder;
use std::io::BufRead;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::sync::{mpsc, Mutex};
use tracing::{error, info, warn};

/// 数据流处理器
/// 负责实际的数据解析和发送，与 DataReader 分离
pub struct DataStreamer {
    status: Arc<Mutex<DataReaderStatus>>,
}

impl DataStreamer {
    pub fn new(status: Arc<Mutex<DataReaderStatus>>) -> Self {
        Self { status }
    }

    /// 启动数据流处理任务
    pub async fn start_streaming(
        &self,
        data_paths: DataTypesPaths,
        output_tx: mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
    ) -> Result<()> {
        info!("Starting data streaming tasks {:?}", data_source_type);

        // 启动 quotes 数据流任务
        if let Some(quotes_path) = data_paths.quotes {
            let output_tx_clone = output_tx.clone();
            let status_clone = self.status.clone();
            let start_time_clone = start_time;
            let end_time_clone = end_time;

            tokio::spawn(async move {
                if let Err(e) = Self::stream_quotes_data(
                    quotes_path,
                    output_tx_clone,
                    start_time_clone,
                    end_time_clone,
                    data_source_type,
                    status_clone,
                )
                .await
                {
                    error!("Quotes streaming failed: {}", e);
                }
            });
        }

        // 启动 trades 数据流任务
        if let Some(trades_path) = data_paths.trades {
            let output_tx_clone = output_tx.clone();
            let status_clone = self.status.clone();
            let start_time_clone = start_time;
            let end_time_clone = end_time;

            tokio::spawn(async move {
                if let Err(e) = Self::stream_trades_data(
                    trades_path,
                    output_tx_clone,
                    start_time_clone,
                    end_time_clone,
                    data_source_type,
                    status_clone,
                )
                .await
                {
                    error!("Trades streaming failed: {}", e);
                }
            });
        }

        // 启动 depth 数据流任务
        if let Some(depth_path) = data_paths.depth {
            let output_tx_clone = output_tx.clone();
            let status_clone = self.status.clone();
            let start_time_clone = start_time;
            let end_time_clone = end_time;

            tokio::spawn(async move {
                if let Err(e) = Self::stream_depth_data(
                    depth_path,
                    output_tx_clone,
                    start_time_clone,
                    end_time_clone,
                    data_source_type,
                    status_clone,
                )
                .await
                {
                    error!("Depth streaming failed: {}", e);
                }
            });
        }

        Ok(())
    }

    /// 流式处理 quotes 数据
    async fn stream_quotes_data(
        quotes_path: PathBuf,
        output_tx: mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: Arc<Mutex<DataReaderStatus>>,
    ) -> Result<()> {
        // 等待状态变为 Reading
        loop {
            let current_status = status.lock().await.clone();
            match current_status {
                DataReaderStatus::Reading => break,
                DataReaderStatus::Stopped => {
                    info!("Quotes streaming stopped before start");
                    return Ok(());
                }
                _ => {
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    continue;
                }
            }
        }

        tracing::debug!("Starting quotes data streaming from: {:?}", quotes_path);

        let files = Self::find_files(&quotes_path).await?;
        let start_file_index = Self::find_start_file(&files, start_time)?;

        if let Some(start_index) = start_file_index {
            for file_path in &files[start_index..] {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        info!("Quotes streaming stopped");
                        return Ok(());
                    }
                }

                tracing::info!("Processing quotes file: {:?}", file_path);
                Self::process_quotes_file(
                    file_path,
                    &output_tx,
                    start_time,
                    end_time,
                    data_source_type,
                    &status,
                )
                .await?;

                // 检查是否超过结束时间
                if Self::file_exceeds_end_time(file_path, end_time) {
                    info!("Reached end time for quotes");
                    break;
                }
            }
        }

        info!("Quotes streaming completed");
        Ok(())
    }

    /// 流式处理 trades 数据
    async fn stream_trades_data(
        trades_path: PathBuf,
        output_tx: mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: Arc<Mutex<DataReaderStatus>>,
    ) -> Result<()> {
        // 等待状态变为 Reading
        loop {
            let current_status = status.lock().await.clone();
            match current_status {
                DataReaderStatus::Reading => break,
                DataReaderStatus::Stopped => {
                    info!("Trades streaming stopped before start");
                    return Ok(());
                }
                _ => {
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    continue;
                }
            }
        }

        info!("Starting trades data streaming from: {:?}", trades_path);

        let files = Self::find_files(&trades_path).await?;
        let start_file_index = Self::find_start_file(&files, start_time)?;

        if let Some(start_index) = start_file_index {
            for file_path in &files[start_index..] {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        info!("Trades streaming stopped");
                        return Ok(());
                    }
                }

                tracing::info!("Processing trades file: {:?}", file_path);
                Self::process_trades_file(
                    file_path,
                    &output_tx,
                    start_time,
                    end_time,
                    data_source_type,
                    &status,
                )
                .await?;

                // 检查是否超过结束时间
                if Self::file_exceeds_end_time(file_path, end_time) {
                    info!("Reached end time for trades");
                    break;
                }
            }
        }

        info!("Trades streaming completed");
        Ok(())
    }

    /// 查找数据文件
    async fn find_files(path: &PathBuf) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        let mut entries = tokio::fs::read_dir(path)
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory: {}", e)))?;

        while let Some(entry) = entries
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if extension == "csv" || extension == "gz" {
                        files.push(path);
                    }
                }
            }
        }

        files.sort();
        Ok(files)
    }

    /// 根据文件名找到包含 start_time 的文件索引
    fn find_start_file(files: &[PathBuf], start_time: DateTime<Utc>) -> Result<Option<usize>> {
        let start_date = start_time.format("%Y-%m-%d").to_string();

        for (index, file_path) in files.iter().enumerate() {
            if let Some(file_name) = file_path.file_name().and_then(|n| n.to_str()) {
                // 检查文件名是否以日期开头（标准格式）
                if file_name.starts_with(&start_date) {
                    tracing::debug!("Found start file: {} at index {}", file_name, index);
                    return Ok(Some(index));
                }

                // 检查文件名是否包含日期（如 Binance depth 格式）
                if file_name.contains(&start_date) {
                    tracing::debug!(
                        "Found start file (contains date): {} at index {}",
                        file_name,
                        index
                    );
                    return Ok(Some(index));
                }
            }
        }

        warn!("No file found for start date: {}", start_date);
        Ok(None)
    }

    /// 检查文件是否超过结束时间
    fn file_exceeds_end_time(file_path: &PathBuf, end_time: DateTime<Utc>) -> bool {
        if let Some(file_name) = file_path.file_name().and_then(|n| n.to_str()) {
            if let Some(date_part) = file_name.split('_').next() {
                if let Ok(file_date) = chrono::NaiveDate::parse_from_str(date_part, "%Y-%m-%d") {
                    let file_datetime = file_date.and_hms_opt(23, 59, 59).unwrap().and_utc();
                    return file_datetime > end_time;
                }
            }
        }
        false
    }

    /// 处理单个 quotes 文件
    async fn process_quotes_file(
        file_path: &PathBuf,
        output_tx: &mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: &Arc<Mutex<DataReaderStatus>>,
    ) -> Result<()> {
        if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
            // 处理压缩文件
            let file = std::fs::File::open(file_path)
                .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
            let decoder = GzDecoder::new(file);
            let reader = std::io::BufReader::new(decoder);

            for line in reader.lines() {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        return Ok(());
                    }
                }

                let line =
                    line.map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?;

                if !line.trim().is_empty() {
                    match DataParser::parse_quotes_csv_line_with_path(
                        &line,
                        data_source_type,
                        Some(file_path.to_string_lossy().as_ref()),
                    ) {
                        Ok(Some(market_data)) => {
                            if DataParser::is_data_in_time_range(&market_data, start_time, end_time)
                            {
                                tracing::debug!(
                                    "📤 Streamer sending quotes data: {:?}",
                                    market_data
                                );
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send quotes data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send data: {}",
                                        e
                                    )));
                                } else {
                                    tracing::debug!("✅ Quotes data sent successfully");
                                }
                            }
                        }
                        Ok(None) => {
                            // 空数据，跳过
                        }
                        Err(e) => {
                            error!("Failed to parse quotes line: {}, line: {}", e, line);
                        }
                    }
                }

                // 小延迟避免过快发送
                tokio::task::yield_now().await;
            }
        } else {
            // 处理未压缩文件
            let file = tokio::fs::File::open(file_path)
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
            let reader = BufReader::new(file);
            let mut lines = reader.lines();

            while let Some(line) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
            {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        return Ok(());
                    }
                }

                if !line.trim().is_empty() {
                    match DataParser::parse_quotes_csv_line_with_path(
                        &line,
                        data_source_type,
                        Some(file_path.to_string_lossy().as_ref()),
                    ) {
                        Ok(Some(market_data)) => {
                            if DataParser::is_data_in_time_range(&market_data, start_time, end_time)
                            {
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send quotes data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send data: {}",
                                        e
                                    )));
                                }
                            }
                        }
                        Ok(None) => {
                            // 空数据，跳过
                        }
                        Err(e) => {
                            error!("Failed to parse quotes line: {}, line: {}", e, line);
                        }
                    }

                    // 小延迟避免过快发送
                    tokio::task::yield_now().await;
                }
            }
        }

        Ok(())
    }

    /// 处理单个 trades 文件
    async fn process_trades_file(
        file_path: &PathBuf,
        output_tx: &mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: &Arc<Mutex<DataReaderStatus>>,
    ) -> Result<()> {
        if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
            // 处理压缩文件
            let file = std::fs::File::open(file_path)
                .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
            let decoder = GzDecoder::new(file);
            let reader = std::io::BufReader::new(decoder);

            for line in reader.lines() {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        return Ok(());
                    }
                }

                let line =
                    line.map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?;

                if !line.trim().is_empty() {
                    match DataParser::parse_trades_csv_line_with_path(
                        &line,
                        data_source_type,
                        Some(file_path.to_string_lossy().as_ref()),
                    ) {
                        Ok(Some(market_data)) => {
                            if DataParser::is_data_in_time_range(&market_data, start_time, end_time)
                            {
                                tracing::debug!(
                                    "📤 Streamer sending trades data: {:?}",
                                    market_data
                                );
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send trades data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send data: {}",
                                        e
                                    )));
                                } else {
                                    tracing::debug!("✅ Trades data sent successfully");
                                }
                            }
                        }
                        Ok(None) => {
                            // 空数据，跳过
                        }
                        Err(e) => {
                            error!("Failed to parse trades line: {}, line: {}", e, line);
                        }
                    }

                    // 小延迟避免过快发送
                    tokio::task::yield_now().await;
                }
            }
        } else {
            // 处理未压缩文件
            let file = tokio::fs::File::open(file_path)
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
            let reader = BufReader::new(file);
            let mut lines = reader.lines();

            while let Some(line) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
            {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        return Ok(());
                    }
                }

                if !line.trim().is_empty() {
                    match DataParser::parse_trades_csv_line_with_path(
                        &line,
                        data_source_type,
                        Some(file_path.to_string_lossy().as_ref()),
                    ) {
                        Ok(Some(market_data)) => {
                            if DataParser::is_data_in_time_range(&market_data, start_time, end_time)
                            {
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send trades data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send data: {}",
                                        e
                                    )));
                                }
                            }
                        }
                        Ok(None) => {
                            // 空数据，跳过
                        }
                        Err(e) => {
                            error!("Failed to parse trades line: {}, line: {}", e, line);
                        }
                    }

                    // 小延迟避免过快发送
                    tokio::task::yield_now().await;
                }
            }
        }

        Ok(())
    }

    /// 流式处理 depth 数据
    async fn stream_depth_data(
        depth_path: PathBuf,
        output_tx: mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: Arc<Mutex<DataReaderStatus>>,
    ) -> Result<()> {
        // 等待状态变为 Reading
        loop {
            let current_status = status.lock().await.clone();
            match current_status {
                DataReaderStatus::Reading => break,
                DataReaderStatus::Stopped => {
                    info!("Depth streaming stopped before start");
                    return Ok(());
                }
                _ => {
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    continue;
                }
            }
        }

        info!("Starting depth data streaming from: {:?}", depth_path);

        let files = Self::find_files(&depth_path).await?;
        let start_file_index = Self::find_start_file(&files, start_time)?;

        if let Some(start_index) = start_file_index {
            for file_path in &files[start_index..] {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        info!("Depth streaming stopped");
                        return Ok(());
                    }
                }

                tracing::debug!("Processing depth file: {:?}", file_path);
                Self::process_depth_file(
                    file_path,
                    &output_tx,
                    start_time,
                    end_time,
                    data_source_type,
                    &status,
                )
                .await?;

                // 检查是否超过结束时间
                if Self::file_exceeds_end_time(file_path, end_time) {
                    info!("Reached end time for depth");
                    break;
                }
            }
        }

        info!("Depth streaming completed");
        Ok(())
    }

    /// 处理单个 depth 文件
    async fn process_depth_file(
        file_path: &PathBuf,
        output_tx: &mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: &Arc<Mutex<DataReaderStatus>>,
    ) -> Result<()> {
        if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
            // 处理压缩文件
            let file = std::fs::File::open(file_path)
                .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
            let decoder = GzDecoder::new(file);
            let reader = std::io::BufReader::new(decoder);

            for line in reader.lines() {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        return Ok(());
                    }
                }

                let line =
                    line.map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?;

                if !line.trim().is_empty() {
                    match DataParser::parse_depth_csv_line_with_path(
                        &line,
                        data_source_type,
                        Some(file_path.to_string_lossy().as_ref()),
                    )
                    .await
                    {
                        Ok(Some(market_data)) => {
                            if DataParser::is_data_in_time_range(&market_data, start_time, end_time)
                            {
                                loop {
                                    if let Err(e) = output_tx.send(market_data.clone()).await {
                                        error!("Failed to send depth data: {}", e);
                                        tokio::time::sleep(std::time::Duration::from_millis(100))
                                            .await;
                                        continue;
                                    }
                                    break;
                                }
                            }
                        }
                        Ok(None) => {
                            tracing::debug!("empty line");
                        }
                        Err(e) => {
                            error!("Failed to parse depth line: {}, line: {}", e, line);
                        }
                    }

                    // 小延迟避免过快发送
                    tokio::task::yield_now().await;
                }
            }
        } else {
            // 处理未压缩文件
            let file = tokio::fs::File::open(file_path)
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
            let reader = BufReader::new(file);
            let mut lines = reader.lines();

            while let Some(line) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
            {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        return Ok(());
                    }
                }

                if !line.trim().is_empty() {
                    match DataParser::parse_depth_csv_line_with_path(
                        &line,
                        data_source_type,
                        Some(file_path.to_string_lossy().as_ref()),
                    )
                    .await
                    {
                        Ok(Some(market_data)) => {
                            if DataParser::is_data_in_time_range(&market_data, start_time, end_time)
                            {
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send depth data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send data: {}",
                                        e
                                    )));
                                }
                            }
                        }
                        Ok(None) => {
                            // 空数据，跳过
                        }
                        Err(e) => {
                            error!("Failed to parse depth line: {}, line: {}", e, line);
                        }
                    }

                    // 小延迟避免过快发送
                    tokio::task::yield_now().await;
                }
            }
        }

        Ok(())
    }
}

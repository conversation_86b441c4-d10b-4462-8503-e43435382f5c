use crate::types::{
    CancelOrderRequest, ClientFormat, Order, OrderSide, OrderStatus, OrderType, Price,
    SubscriptionType,
};
use crate::websocket::subscription::{ClientId, SubscriptionManager};
use crate::{BacktestError, Result};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::mpsc;
use tokio_tungstenite::tungstenite::Message;
use tracing::{debug, error, info, warn};

/// Binance风格的订阅消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceSubscribeMessage {
    pub method: String,
    pub params: Vec<String>,
    pub id: Option<u64>,
}

/// Binance风格的取消订阅消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceUnsubscribeMessage {
    pub method: String,
    pub params: Vec<String>,
    pub id: Option<u64>,
}

/// Binance风格的响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceResponse {
    pub result: Option<serde_json::Value>,
    pub status: i64,
    pub id: Option<u64>,
    pub error: Option<BinanceError>,
}

/// Binance风格的下单请求消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceOrderRequest {
    pub id: Option<serde_json::Value>,
    pub method: String,
    pub params: BinanceOrderParams,
}

/// Binance风格的下单参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceOrderParams {
    #[serde(rename = "apiKey")]
    pub api_key: Option<String>,
    pub symbol: String,
    pub side: String, // "BUY" or "SELL"
    #[serde(rename = "type")]
    pub order_type: String, // "LIMIT", "MARKET", etc.
    #[serde(rename = "positionSide")]
    pub position_side: Option<String>, // "BOTH", "LONG", "SHORT"
    #[serde(rename = "timeInForce")]
    pub time_in_force: Option<String>, // "GTC", "IOC", "FOK"
    pub quantity: Option<String>,
    pub price: Option<String>,
    #[serde(rename = "newClientOrderId")]
    pub new_client_order_id: Option<String>,
    #[serde(rename = "reduceOnly")]
    pub reduce_only: Option<String>,
    #[serde(rename = "stopPrice")]
    pub stop_price: Option<String>,
    #[serde(rename = "workingType")]
    pub working_type: Option<String>,
    #[serde(rename = "priceProtect")]
    pub price_protect: Option<String>,
    #[serde(rename = "selfTradePreventionMode")]
    pub self_trade_prevention_mode: Option<String>,
    pub timestamp: Option<u64>,
    pub signature: Option<String>,
    #[serde(rename = "recvWindow")]
    pub recv_window: Option<u64>,
}

/// Binance风格的下单响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceOrderResponse {
    #[serde(rename = "orderId")]
    pub order_id: u64,
}

/// Binance风格的取消订单请求消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceCancelOrderRequest {
    pub id: Option<serde_json::Value>,
    pub method: String,
    pub params: BinanceCancelOrderParams,
}

/// Binance风格的取消订单参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceCancelOrderParams {
    #[serde(rename = "apiKey")]
    pub api_key: Option<String>,
    pub symbol: String,
    #[serde(rename = "orderId")]
    pub order_id: Option<u64>,
    #[serde(rename = "origClientOrderId")]
    pub orig_client_order_id: Option<String>,
    pub timestamp: Option<u64>,
    pub signature: Option<String>,
    #[serde(rename = "recvWindow")]
    pub recv_window: Option<u64>,
}

/// Binance风格的取消订单响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceCancelOrderResponse {
    #[serde(rename = "clientOrderId")]
    pub client_order_id: String,
    #[serde(rename = "cumQty")]
    pub cum_qty: String,
    #[serde(rename = "cumQuote")]
    pub cum_quote: String,
    #[serde(rename = "executedQty")]
    pub executed_qty: String,
    #[serde(rename = "orderId")]
    pub order_id: u64,
    #[serde(rename = "origQty")]
    pub orig_qty: String,
    #[serde(rename = "origType")]
    pub orig_type: String,
    pub price: String,
    #[serde(rename = "reduceOnly")]
    pub reduce_only: bool,
    pub side: String,
    #[serde(rename = "positionSide")]
    pub position_side: Option<String>,
    pub status: String,
    #[serde(rename = "stopPrice")]
    pub stop_price: Option<String>,
    #[serde(rename = "closePosition")]
    pub close_position: Option<bool>,
    pub symbol: String,
    #[serde(rename = "timeInForce")]
    pub time_in_force: String,
    #[serde(rename = "type")]
    pub order_type: String,
    #[serde(rename = "activatePrice")]
    pub activate_price: Option<String>,
    #[serde(rename = "priceRate")]
    pub price_rate: Option<String>,
    #[serde(rename = "updateTime")]
    pub update_time: u64,
    #[serde(rename = "workingType")]
    pub working_type: Option<String>,
    #[serde(rename = "priceProtect")]
    pub price_protect: Option<bool>,
    #[serde(rename = "priceMatch")]
    pub price_match: Option<String>,
    #[serde(rename = "selfTradePreventionMode")]
    pub self_trade_prevention_mode: Option<String>,
    #[serde(rename = "goodTillDate")]
    pub good_till_date: Option<u64>,
}

/// Binance ORDER_TRADE_UPDATE 事件格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceOrderTradeUpdate {
    #[serde(rename = "e")]
    pub event_type: String, // "ORDER_TRADE_UPDATE"
    #[serde(rename = "E")]
    pub event_time: u64, // Event Time
    #[serde(rename = "T")]
    pub transaction_time: u64, // Transaction Time
    #[serde(rename = "o")]
    pub order: BinanceOrderData,
}

/// Binance 订单数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceOrderData {
    #[serde(rename = "s")]
    pub symbol: String, // Symbol
    #[serde(rename = "c")]
    pub client_order_id: String, // Client Order Id
    #[serde(rename = "S")]
    pub side: String, // Side
    #[serde(rename = "o")]
    pub order_type: String, // Order Type
    #[serde(rename = "f")]
    pub time_in_force: String, // Time in Force
    #[serde(rename = "q")]
    pub original_quantity: String, // Original Quantity
    #[serde(rename = "p")]
    pub original_price: String, // Original Price
    #[serde(rename = "ap")]
    pub average_price: String, // Average Price
    #[serde(rename = "sp")]
    pub stop_price: String, // Stop Price
    #[serde(rename = "x")]
    pub execution_type: String, // Execution Type
    #[serde(rename = "X")]
    pub order_status: String, // Order Status
    #[serde(rename = "i")]
    pub order_id: u64, // Order Id
    #[serde(rename = "l")]
    pub last_filled_quantity: String, // Order Last Filled Quantity
    #[serde(rename = "z")]
    pub filled_quantity: String, // Order Filled Accumulated Quantity
    #[serde(rename = "L")]
    pub last_filled_price: String, // Last Filled Price
    #[serde(rename = "N")]
    pub commission_asset: String, // Commission Asset
    #[serde(rename = "n")]
    pub commission: String, // Commission
    #[serde(rename = "T")]
    pub order_trade_time: u64, // Order Trade Time
    #[serde(rename = "t")]
    pub trade_id: u64, // Trade Id
    #[serde(rename = "b")]
    pub bids_notional: String, // Bids Notional
    #[serde(rename = "a")]
    pub ask_notional: String, // Ask Notional
    #[serde(rename = "m")]
    pub is_maker: bool, // Is this trade the maker side?
    #[serde(rename = "R")]
    pub reduce_only: bool, // Is this reduce only
    #[serde(rename = "wt")]
    pub working_type: String, // Stop Price Working Type
    #[serde(rename = "ot")]
    pub original_order_type: String, // Original Order Type
    #[serde(rename = "ps")]
    pub position_side: String, // Position Side
    #[serde(rename = "cp")]
    pub close_position: bool, // If Close-All
    #[serde(rename = "AP")]
    pub activation_price: String, // Activation Price
    #[serde(rename = "cr")]
    pub callback_rate: String, // Callback Rate
    #[serde(rename = "pP")]
    pub price_protect: bool, // If price protection is turned on
    #[serde(rename = "si")]
    pub ignore_si: u64, // ignore
    #[serde(rename = "ss")]
    pub ignore_ss: u64, // ignore
    #[serde(rename = "rp")]
    pub realized_profit: String, // Realized Profit of the trade
    #[serde(rename = "V")]
    pub stp_mode: String, // STP mode
    #[serde(rename = "pm")]
    pub price_match_mode: String, // Price match mode
    #[serde(rename = "gtd")]
    pub gtd_auto_cancel_time: u64, // TIF GTD order auto cancel time
}

/// Binance ACCOUNT_UPDATE 事件格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceAccountUpdate {
    #[serde(rename = "e")]
    pub event_type: String, // "ACCOUNT_UPDATE"
    #[serde(rename = "E")]
    pub event_time: u64, // Event Time
    #[serde(rename = "T")]
    pub transaction_time: u64, // Transaction Time
    #[serde(rename = "a")]
    pub account: BinanceAccountUpdateData,
}

/// Binance 账户更新数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceAccountUpdateData {
    #[serde(rename = "m")]
    pub event_reason_type: String, // e.g., "ORDER", "FUNDING_FEE"
    #[serde(rename = "B")]
    pub balances: Vec<BinanceAccountBalance>,
    #[serde(rename = "P")]
    pub positions: Vec<BinanceAccountPosition>,
}

/// Binance 账户余额条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceAccountBalance {
    #[serde(rename = "a")]
    pub asset: String, // Asset, e.g., "USDT"
    #[serde(rename = "wb")]
    pub wallet_balance: String, // Wallet Balance
    #[serde(rename = "cw")]
    pub cross_wallet_balance: String, // Cross Wallet Balance
    #[serde(rename = "bc")]
    pub balance_change: String, // Balance Change since last event

                                // bc（余额变化）在部分文档中可选，这里不强制包含
}

/// Binance 仓位条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceAccountPosition {
    #[serde(rename = "s")]
    pub symbol: String, // Symbol, e.g., "BTCUSDT"
    #[serde(rename = "pa")]
    pub position_amount: String, // Position Amount
    #[serde(rename = "ep")]
    pub entry_price: String, // Entry Price
    #[serde(rename = "cr")]
    pub accumulated_realized: String, // Accumulated Realized
    #[serde(rename = "up")]
    pub unrealized_pnl: String, // Unrealized PnL
    #[serde(rename = "mt")]
    pub margin_type: String, // "cross" or "isolated"
    #[serde(rename = "iw")]
    pub isolated_wallet: String, // Isolated Wallet amount
    #[serde(rename = "ps")]
    pub position_side: String, // "BOTH", "LONG", or "SHORT"
}

/// Binance错误信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceError {
    pub code: i32,
    pub msg: String,
}

/// Book Ticker数据格式（Binance兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceBookTicker {
    #[serde(rename = "e")]
    pub event_type: String,
    #[serde(rename = "E")]
    pub event_time: u64,
    #[serde(rename = "T")]
    pub transaction_time: u64,
    #[serde(rename = "s")]
    pub symbol: String,
    #[serde(rename = "u")]
    pub update_id: u64,
    #[serde(rename = "b")]
    pub best_bid_price: String,
    #[serde(rename = "B")]
    pub best_bid_qty: String,
    #[serde(rename = "a")]
    pub best_ask_price: String,
    #[serde(rename = "A")]
    pub best_ask_qty: String,
}

/// Depth Update数据格式（Binance兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceDepthUpdate {
    #[serde(rename = "e")]
    pub event_type: String,
    #[serde(rename = "E")]
    pub event_time: u64,
    #[serde(rename = "T")]
    pub transaction_time: u64,
    #[serde(rename = "s")]
    pub symbol: String,
    #[serde(rename = "U")]
    pub first_update_id: u64,
    #[serde(rename = "u")]
    pub final_update_id: u64,
    #[serde(rename = "pu")]
    pub prev_final_update_id: u64,
    #[serde(rename = "b")]
    pub bids: Vec<[String; 2]>,
    #[serde(rename = "a")]
    pub asks: Vec<[String; 2]>,
}

/// Aggregate Trade数据格式（Binance兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceTrade {
    #[serde(rename = "e")]
    pub event_type: String,
    #[serde(rename = "E")]
    pub event_time: u64,
    #[serde(rename = "s")]
    pub symbol: String,
    #[serde(rename = "t")]
    pub trade_id: u64,
    #[serde(rename = "p")]
    pub price: String,
    #[serde(rename = "q")]
    pub quantity: String,
    #[serde(rename = "T")]
    pub trade_time: u64,
    #[serde(rename = "m")]
    pub is_buyer_maker: bool,
}

/// 组合流包装格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamWrapper {
    pub stream: String,
    pub data: serde_json::Value,
}

/// OKX订阅消息格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OkxSubscribeMessage {
    pub op: String, // "subscribe"
    pub args: Vec<OkxChannelArg>,
}

/// OKX频道参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OkxChannelArg {
    pub channel: String,
    #[serde(rename = "instId")]
    pub inst_id: String, // 交易对，如 "BTC-USDT"
}

/// OKX响应消息格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OkxResponse {
    pub event: Option<String>, // "subscribe", "unsubscribe", "error"
    pub arg: Option<OkxChannelArg>,
    pub code: Option<String>,
    pub msg: Option<String>,
    #[serde(rename = "connId")]
    pub conn_id: Option<String>,
}

/// OKX数据推送格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OkxDataMessage {
    pub arg: OkxChannelArg,
    pub data: Vec<serde_json::Value>,
}

/// OKX Ticker数据格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OkxTicker {
    #[serde(rename = "instType")]
    pub inst_type: String,
    #[serde(rename = "instId")]
    pub inst_id: String,
    pub last: String, // 最新成交价
    #[serde(rename = "lastSz")]
    pub last_sz: String, // 最新成交数量
    #[serde(rename = "askPx")]
    pub ask_px: String, // 卖一价
    #[serde(rename = "askSz")]
    pub ask_sz: String, // 卖一数量
    #[serde(rename = "bidPx")]
    pub bid_px: String, // 买一价
    #[serde(rename = "bidSz")]
    pub bid_sz: String, // 买一数量
    pub open24h: String, // 24小时开盘价
    pub high24h: String, // 24小时最高价
    pub low24h: String, // 24小时最低价
    #[serde(rename = "volCcy24h")]
    pub vol_ccy_24h: String, // 24小时成交量（计价货币）
    pub vol24h: String, // 24小时成交量（交易货币）
    pub ts: String,   // 数据产生时间戳
    #[serde(rename = "sodUtc0")]
    pub sod_utc0: String, // UTC+0时区今日开盘价
    #[serde(rename = "sodUtc8")]
    pub sod_utc8: String, // UTC+8时区今日开盘价
}

#[derive(Clone, Serialize, Debug)]
pub struct OkxSymbol {
    pub base: String,
    pub quote: String,
    pub contract_type: String,
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxDepthResp {
    pub symbol: String,
    pub bids: Vec<(String, String)>,
    pub asks: Vec<(String, String)>,
    pub ts: String,
}

/// OKX Trade数据格式
#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxTrade {
    pub inst_id: String,
    pub trade_id: String,
    pub ts: String,
    pub px: String,
    pub sz: String,
    pub side: String,
}

/// WebSocket消息类型（保持向后兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WebSocketMessage {
    /// 订阅请求
    Subscribe { subscription: SubscriptionType },
    /// 取消订阅请求
    Unsubscribe { subscription: SubscriptionType },
    /// 心跳
    Ping,
    /// 心跳响应
    Pong,
    /// 错误消息
    Error { message: String },
    /// 成功响应
    Success { message: String },
    /// 数据推送
    Data {
        subscription: SubscriptionType,
        data: serde_json::Value,
    },
}

/// WebSocket处理器
pub struct WebSocketHandler {
    client_id: ClientId,
    subscription_manager: Arc<SubscriptionManager>,
    message_tx: mpsc::Sender<String>,
    /// 当前订阅的流（用于Binance风格的订阅）
    subscribed_streams: HashMap<String, SubscriptionType>,
    /// 订单发送通道
    order_tx: Option<mpsc::Sender<Order>>,
    /// 取消订单发送通道
    cancel_order_tx: Option<mpsc::Sender<CancelOrderRequest>>,
    /// 客户端格式，在第一个消息中检测
    client_format: Option<crate::types::ClientFormat>,
}

impl WebSocketHandler {
    /// 创建新的WebSocket处理器
    pub fn new(
        client_id: ClientId,
        subscription_manager: Arc<SubscriptionManager>,
        message_tx: mpsc::Sender<String>,
        order_tx: Option<mpsc::Sender<Order>>,
        cancel_order_tx: Option<mpsc::Sender<CancelOrderRequest>>,
    ) -> Self {
        Self {
            client_id,
            subscription_manager,
            message_tx,
            subscribed_streams: HashMap::new(),
            order_tx,
            cancel_order_tx,
            client_format: None, // 初始时未知，在第一个消息中检测
        }
    }

    /// 处理WebSocket消息
    pub async fn handle_message(&mut self, message: Message) -> Result<()> {
        match message {
            Message::Text(text) => {
                self.handle_text_message(text).await?;
            }
            Message::Binary(_) => {
                warn!(
                    "Received binary message from client {}, ignoring",
                    self.client_id
                );
            }
            Message::Ping(payload) => {
                debug!("Received ping from client {}", self.client_id);
                self.send_pong(payload).await?;
            }
            Message::Pong(_) => {
                debug!("Received pong from client {}", self.client_id);
            }
            Message::Close(_) => {
                info!("Client {} requested close", self.client_id);
                self.handle_disconnect().await;
            }
            Message::Frame(_) => {
                // 原始帧，通常不需要处理
            }
        }

        Ok(())
    }

    /// 处理文本消息
    async fn handle_text_message(&mut self, text: String) -> Result<()> {
        // 如果客户端格式未确定，尝试检测
        if self.client_format.is_none() {
            self.detect_and_set_client_format(&text).await?;
        }

        debug!("Received message from client {}: {}", self.client_id, text);

        // 首先尝试解析为Binance风格的下单请求
        if let Ok(order_msg) = serde_json::from_str::<BinanceOrderRequest>(&text) {
            if order_msg.method == "order.place" {
                return self.handle_binance_order_place(order_msg).await;
            }
        }

        // 尝试解析为Binance风格的取消订单请求
        if let Ok(cancel_msg) = serde_json::from_str::<BinanceCancelOrderRequest>(&text) {
            if cancel_msg.method == "order.cancel" {
                return self.handle_binance_order_cancel(cancel_msg).await;
            }
        }

        // 尝试解析为Binance风格的订阅消息
        if let Ok(subscribe_msg) = serde_json::from_str::<BinanceSubscribeMessage>(&text) {
            if subscribe_msg.method == "SUBSCRIBE" {
                return self.handle_binance_subscribe(subscribe_msg).await;
            }
        }

        // 尝试解析为Binance风格的取消订阅消息
        if let Ok(unsubscribe_msg) = serde_json::from_str::<BinanceUnsubscribeMessage>(&text) {
            if unsubscribe_msg.method == "UNSUBSCRIBE" {
                return self.handle_binance_unsubscribe(unsubscribe_msg).await;
            }
        }

        // 尝试解析为OKX风格的订阅消息
        if let Ok(okx_msg) = serde_json::from_str::<OkxSubscribeMessage>(&text) {
            if okx_msg.op == "subscribe" {
                return self.handle_okx_subscribe(okx_msg).await;
            }
        }

        // 回退到原有的消息格式（保持向后兼容）
        if let Ok(ws_message) = serde_json::from_str::<WebSocketMessage>(&text) {
            match ws_message {
                WebSocketMessage::Ping => {
                    self.send_pong(Vec::new()).await?;
                }
                _ => {
                    warn!("Unexpected message type from client {}", self.client_id);
                    self.send_error("Unexpected message type".to_string())
                        .await?;
                }
            }
            return Ok(());
        }

        warn!(
            "Unknown message format from client {}: {}",
            self.client_id, text
        );
        self.send_binance_error("Unknown message format", None)
            .await?;
        Ok(())
    }

    /// 处理Binance风格的订阅请求
    async fn handle_binance_subscribe(&mut self, msg: BinanceSubscribeMessage) -> Result<()> {
        let mut subscribed_streams = Vec::new();
        debug!("sub: {:?}", msg);

        for stream in &msg.params {
            if let Some(subscription_type) = self.parse_stream_name(stream) {
                if self.subscription_manager.subscribe(
                    &self.client_id,
                    subscription_type.clone(),
                    ClientFormat::Binance,
                ) {
                    self.subscribed_streams
                        .insert(stream.clone(), subscription_type);
                    subscribed_streams.push(stream.clone());
                }
            } else {
                warn!("Unknown stream name: {}", stream);
                self.send_binance_error(
                    &format!("Unknown stream: {}", stream),
                    msg.id
                        .map(|v| serde_json::Value::Number(serde_json::Number::from(v))),
                )
                .await?;
                return Ok(());
            }
        }

        // 发送成功响应
        let response = BinanceResponse {
            result: Some(serde_json::json!(null)),
            id: msg.id,
            status: 200,
            error: None,
        };

        self.send_binance_message(serde_json::to_string(&response)?)
            .await?;
        Ok(())
    }

    /// 处理Binance风格的取消订阅请求
    async fn handle_binance_unsubscribe(&mut self, msg: BinanceUnsubscribeMessage) -> Result<()> {
        for stream in &msg.params {
            if let Some(subscription_type) = self.subscribed_streams.remove(stream) {
                self.subscription_manager
                    .unsubscribe(&self.client_id, &subscription_type);
                info!(
                    "Client {} unsubscribed from stream: {}",
                    self.client_id, stream
                );
            }
        }

        // 发送成功响应
        let response = BinanceResponse {
            result: Some(serde_json::json!(null)),
            id: msg.id,
            status: 200,
            error: None,
        };

        self.send_binance_message(serde_json::to_string(&response)?)
            .await?;
        Ok(())
    }

    /// 处理Binance风格的下单请求
    async fn handle_binance_order_place(&self, msg: BinanceOrderRequest) -> Result<()> {
        info!(
            "Placing order: {} side={} price={:?}",
            msg.params.new_client_order_id.as_ref().unwrap(),
            msg.params.side,
            msg.params.price,
        );

        // 验证必需参数
        if let Err(error_msg) = self.validate_order_params(&msg.params) {
            self.send_binance_error(&error_msg, msg.id).await?;
            return Ok(());
        }

        // 检查是否有订单通道
        if let Some(order_tx) = &self.order_tx {
            // 转换为内部Order类型
            let order = self.convert_binance_order_to_internal(&msg.params)?;
            debug!("binance format order: {:?}", order);

            // 发送订单到matching engine
            if let Err(e) = order_tx.send(order).await {
                error!("Failed to send order to matching engine: {}", e);
                self.send_binance_error("Internal server error", msg.id)
                    .await?;
                return Ok(());
            }

            // 发送确认响应（订单已接受）
            let order_response = self.create_order_response(&msg.params);
            let response = BinanceResponse {
                result: Some(serde_json::to_value(&order_response)?),
                id: msg.id.and_then(|v| v.as_u64()),
                status: 200,
                error: None,
            };
            self.send_binance_message(serde_json::to_string(&response).unwrap())
                .await?;
        } else {
            // 如果没有订单通道，返回错误
            self.send_binance_error("Order processing not available", msg.id)
                .await?;
        }

        Ok(())
    }

    /// 处理Binance风格的取消订单请求
    async fn handle_binance_order_cancel(&self, msg: BinanceCancelOrderRequest) -> Result<()> {
        debug!(
            "Client {} cancelling order: symbol={}, orderId={:?}, origClientOrderId={:?}",
            self.client_id, msg.params.symbol, msg.params.order_id, msg.params.orig_client_order_id
        );

        // 验证必需参数
        if let Err(error_msg) = self.validate_cancel_order_params(&msg.params) {
            self.send_binance_error(&error_msg, msg.id).await?;
            return Ok(());
        }

        // 检查是否有取消订单通道
        if let Some(cancel_order_tx) = &self.cancel_order_tx {
            // 创建取消订单请求
            let cancel_request = CancelOrderRequest {
                order_id: msg.params.order_id.map(|id| id.to_string()),
                client_order_id: msg.params.orig_client_order_id.clone(),
                symbol: msg.params.symbol.clone(),
                timestamp: Utc::now(),
            };

            // 发送取消订单请求到matching engine
            if let Err(e) = cancel_order_tx.send(cancel_request).await {
                error!(
                    "Failed to send cancel order request to matching engine: {}",
                    e
                );
                self.send_binance_error("Internal server error", msg.id)
                    .await?;
                return Ok(());
            }

            // 发送确认响应（取消请求已接受）
            // 注意：实际的取消结果会通过订单更新推送返回
            let cancel_response = self.create_cancel_order_response(&msg.params);
            let response = BinanceResponse {
                result: Some(serde_json::to_value(&cancel_response)?),
                id: msg.id.and_then(|v| v.as_u64()),
                status: 200,
                error: None,
            };
            self.send_binance_message(serde_json::to_string(&response).unwrap())
                .await?;
        } else {
            // 如果没有取消订单通道，返回错误
            self.send_binance_error("Order cancellation not available", msg.id)
                .await?;
        }

        Ok(())
    }

    /// 发送订单到matching engine（用于测试）
    pub async fn send_order_to_engine(&self, order: Order) -> Result<()> {
        if let Some(order_tx) = &self.order_tx {
            order_tx.send(order).await.map_err(|e| {
                BacktestError::WebSocket(format!("Failed to send order to engine: {}", e))
            })?;
        }
        Ok(())
    }

    /// 转换Binance订单参数为内部Order类型
    pub fn convert_binance_order_to_internal(&self, params: &BinanceOrderParams) -> Result<Order> {
        // 解析订单方向
        let side = match params.side.to_uppercase().as_str() {
            "BUY" => OrderSide::Buy,
            "SELL" => OrderSide::Sell,
            _ => return Err(BacktestError::WebSocket("Invalid order side".to_string())),
        };

        // 解析订单类型
        let order_type = match params.order_type.to_uppercase().as_str() {
            "MARKET" => OrderType::Market,
            "LIMIT" => {
                // 检查timeInForce参数来区分不同类型的限价单
                match params.time_in_force.as_deref() {
                    Some("IOC") => OrderType::LimitIOC,
                    Some("GTX") => OrderType::LimitGTX,
                    _ => OrderType::Limit,
                }
            }
            _ => return Err(BacktestError::WebSocket("Invalid order type".to_string())),
        };

        // 解析价格（所有限价单类型都必须有价格）
        let price = if matches!(
            order_type,
            OrderType::Limit | OrderType::LimitIOC | OrderType::LimitGTX
        ) {
            match &params.price {
                Some(price_str) => {
                    let price_value = price_str.parse::<f64>().map_err(|_| {
                        BacktestError::WebSocket("Invalid price format".to_string())
                    })?;
                    Some(Price::new(price_value))
                }
                None => {
                    return Err(BacktestError::WebSocket(
                        "Limit order requires price".to_string(),
                    ))
                }
            }
        } else {
            None
        };

        // 解析数量
        let quantity = params
            .quantity
            .as_ref()
            .ok_or_else(|| BacktestError::WebSocket("Quantity is required".to_string()))?
            .parse::<f64>()
            .map_err(|_| BacktestError::WebSocket("Invalid quantity format".to_string()))?;

        // 生成订单ID
        let order_id = format!(
            "order_{}_{}",
            self.client_id,
            chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0)
        );

        Ok(Order {
            id: order_id,
            client_order_id: params.new_client_order_id.clone().unwrap_or("".to_string()),
            symbol: params.symbol.clone(),
            order_type,
            side,
            price,
            quantity,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
            execution_info: None,
        })
    }

    /// 验证下单参数
    fn validate_order_params(
        &self,
        params: &BinanceOrderParams,
    ) -> std::result::Result<(), String> {
        // 验证symbol
        if params.symbol.is_empty() {
            return Err("Symbol is required".to_string());
        }

        // 验证side
        if params.side != "BUY" && params.side != "SELL" {
            return Err("Side must be BUY or SELL".to_string());
        }

        // 验证order type
        match params.order_type.as_str() {
            "MARKET"
            | "LIMIT"
            | "STOP"
            | "STOP_MARKET"
            | "TAKE_PROFIT"
            | "TAKE_PROFIT_MARKET"
            | "TRAILING_STOP_MARKET" => {}
            _ => return Err(format!("Invalid order type: {}", params.order_type)),
        }

        // 对于LIMIT订单，价格是必需的
        if params.order_type == "LIMIT" && params.price.is_none() {
            return Err("Price is required for LIMIT orders".to_string());
        }

        // 验证timeInForce参数
        if let Some(tif) = &params.time_in_force {
            match tif.as_str() {
                "GTC" | "IOC" | "FOK" | "GTX" => {}
                _ => return Err(format!("Invalid timeInForce: {}", tif)),
            }
        }

        // 对于MARKET订单以外的订单，数量是必需的
        if params.order_type != "MARKET" && params.quantity.is_none() {
            return Err("Quantity is required".to_string());
        }

        // 验证position side (仅适用于期货)
        if let Some(position_side) = &params.position_side {
            if position_side != "BOTH" && position_side != "LONG" && position_side != "SHORT" {
                return Err("Position side must be BOTH, LONG, or SHORT".to_string());
            }
        }

        Ok(())
    }

    /// 创建模拟订单响应
    fn create_order_response(&self, _params: &BinanceOrderParams) -> BinanceOrderResponse {
        let order_id = chrono::Utc::now().timestamp_millis() as u64;
        BinanceOrderResponse { order_id }
    }

    /// 验证取消订单参数
    fn validate_cancel_order_params(
        &self,
        params: &BinanceCancelOrderParams,
    ) -> std::result::Result<(), String> {
        // 检查symbol是否存在
        if params.symbol.is_empty() {
            return Err("Symbol is required".to_string());
        }

        // 检查是否提供了orderId或origClientOrderId中的至少一个
        if params.order_id.is_none() && params.orig_client_order_id.is_none() {
            return Err("Either orderId or origClientOrderId must be provided".to_string());
        }

        Ok(())
    }

    /// 创建模拟取消订单响应
    fn create_cancel_order_response(
        &self,
        params: &BinanceCancelOrderParams,
    ) -> BinanceCancelOrderResponse {
        let order_id = params
            .order_id
            .unwrap_or_else(|| chrono::Utc::now().timestamp_millis() as u64);
        let client_order_id = params
            .orig_client_order_id
            .clone()
            .unwrap_or_else(|| "".to_string());

        BinanceCancelOrderResponse {
            client_order_id,
            cum_qty: "0".to_string(),
            cum_quote: "0".to_string(),
            executed_qty: "0".to_string(),
            order_id,
            orig_qty: "0".to_string(),
            orig_type: "LIMIT".to_string(),
            price: "0".to_string(),
            reduce_only: false,
            side: "BUY".to_string(),
            position_side: Some("BOTH".to_string()),
            status: "CANCELED".to_string(),
            stop_price: None,
            close_position: Some(false),
            symbol: params.symbol.clone(),
            time_in_force: "GTC".to_string(),
            order_type: "LIMIT".to_string(),
            activate_price: None,
            price_rate: None,
            update_time: chrono::Utc::now().timestamp_millis() as u64,
            working_type: Some("CONTRACT_PRICE".to_string()),
            price_protect: Some(false),
            price_match: Some("NONE".to_string()),
            self_trade_prevention_mode: Some("NONE".to_string()),
            good_till_date: Some(0),
        }
    }

    /// 解析流名称为订阅类型
    fn parse_stream_name(&self, stream: &str) -> Option<SubscriptionType> {
        if stream.ends_with("@bookTicker") {
            Some(SubscriptionType::BookTicker)
        } else if stream.ends_with("@trade") {
            Some(SubscriptionType::Trade)
        } else if stream.contains("@depth") {
            // 支持 @depth5, @depth10, @depth20, @depth5@100ms 等格式
            Some(SubscriptionType::OrderBook)
        } else if stream.contains("listen_key") {
            Some(SubscriptionType::OrderAndFill)
        } else {
            None
        }
    }

    /// 解析OKX频道为订阅类型
    fn parse_okx_channel(&self, channel: &str) -> Option<SubscriptionType> {
        match channel {
            "bbo-tbt" => Some(SubscriptionType::BookTicker),
            "trades" => Some(SubscriptionType::Trade),
            "books" | "books5" | "books-l2-tbt" => Some(SubscriptionType::OrderBook),
            _ => None,
        }
    }

    /// 处理OKX风格的订阅请求
    async fn handle_okx_subscribe(&mut self, msg: OkxSubscribeMessage) -> Result<()> {
        let mut subscribed_channels = Vec::new();

        for arg in &msg.args {
            if let Some(subscription_type) = self.parse_okx_channel(&arg.channel) {
                if self.subscription_manager.subscribe(
                    &self.client_id,
                    subscription_type.clone(),
                    ClientFormat::Okx,
                ) {
                    // 为OKX格式创建一个唯一的流标识符
                    let stream_key = format!("{}:{}", arg.channel, arg.inst_id);
                    self.subscribed_streams
                        .insert(stream_key.clone(), subscription_type);
                    subscribed_channels.push(stream_key);
                    info!(
                        "Client {} subscribed to OKX channel: {} for {}",
                        self.client_id, arg.channel, arg.inst_id
                    );
                }
            } else {
                warn!("Unknown OKX channel: {}", arg.channel);
                self.send_okx_error(&format!("Unknown channel: {}", arg.channel), &arg)
                    .await?;
                return Ok(());
            }
        }

        // 发送成功响应
        let response = OkxResponse {
            event: Some("subscribe".to_string()),
            arg: msg.args.first().cloned(),
            code: Some("0".to_string()),
            msg: Some("".to_string()),
            conn_id: Some(self.client_id.to_string()),
        };

        self.send_okx_message(serde_json::to_string(&response)?)
            .await?;
        Ok(())
    }

    /// 发送OKX风格的错误消息
    async fn send_okx_error(&self, message: &str, arg: &OkxChannelArg) -> Result<()> {
        let response = OkxResponse {
            event: Some("error".to_string()),
            arg: Some(arg.clone()),
            code: Some("60018".to_string()), // OKX错误代码
            msg: Some(message.to_string()),
            conn_id: Some(self.client_id.to_string()),
        };

        self.send_okx_message(serde_json::to_string(&response)?)
            .await
    }

    /// 发送OKX消息给客户端
    async fn send_okx_message(&self, message: String) -> Result<()> {
        self.message_tx
            .send(message)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("Failed to send OKX message: {}", e)))
    }

    /// 检测并设置客户端格式
    async fn detect_and_set_client_format(&mut self, message: &str) -> Result<()> {
        let format = if let Ok(okx_msg) = serde_json::from_str::<OkxSubscribeMessage>(message) {
            if okx_msg.op == "subscribe" {
                crate::types::ClientFormat::Okx
            } else {
                crate::types::ClientFormat::Binance // 默认
            }
        } else if let Ok(_) = serde_json::from_str::<BinanceSubscribeMessage>(message) {
            crate::types::ClientFormat::Binance
        } else if let Ok(_) = serde_json::from_str::<BinanceUnsubscribeMessage>(message) {
            crate::types::ClientFormat::Binance
        } else if let Ok(_) = serde_json::from_str::<BinanceOrderRequest>(message) {
            crate::types::ClientFormat::Binance
        } else {
            // 默认为Binance格式
            crate::types::ClientFormat::Binance
        };

        self.client_format = Some(format.clone());

        // 更新订阅管理器中的客户端格式
        // 注意：这里需要重新注册客户端以更新格式
        // 由于当前架构限制，我们暂时记录格式但不重新注册
        debug!(
            "Detected client format for {}: {:?}",
            self.client_id, format
        );

        Ok(())
    }

    /// 发送Binance风格的错误消息
    async fn send_binance_error(&self, message: &str, id: Option<serde_json::Value>) -> Result<()> {
        let response = BinanceResponse {
            result: None,
            id: id.and_then(|v| v.as_u64()),
            status: 200,
            error: Some(BinanceError {
                code: -1,
                msg: message.to_string(),
            }),
        };

        self.send_binance_message(serde_json::to_string(&response)?)
            .await
    }

    /// 发送Binance消息给客户端
    async fn send_binance_message(&self, message: String) -> Result<()> {
        self.message_tx
            .send(message)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("Failed to send message: {}", e)))
    }

    /// 发送Pong消息
    async fn send_pong(&self, _payload: Vec<u8>) -> Result<()> {
        // 这里应该发送Pong帧，但由于我们使用的是文本消息通道
        // 我们发送一个Pong类型的WebSocket消息
        let response = WebSocketMessage::Pong;
        self.send_message(response).await
    }

    /// 发送错误消息
    async fn send_error(&self, error_message: String) -> Result<()> {
        let response = WebSocketMessage::Error {
            message: error_message,
        };
        self.send_message(response).await
    }

    /// 发送WebSocket消息
    async fn send_message(&self, message: WebSocketMessage) -> Result<()> {
        let json = serde_json::to_string(&message)
            .map_err(|e| BacktestError::WebSocket(format!("Failed to serialize message: {}", e)))?;

        self.message_tx
            .send(json)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("Failed to send message: {}", e)))?;

        Ok(())
    }

    /// 处理客户端断开连接
    async fn handle_disconnect(&self) {
        info!("Handling disconnect for client {}", self.client_id);
        self.subscription_manager.remove_client(&self.client_id);
    }

    /// 获取客户端ID
    pub fn client_id(&self) -> ClientId {
        self.client_id
    }

    /// 获取客户端订阅信息
    pub fn get_subscriptions(&self) -> Option<std::collections::HashSet<SubscriptionType>> {
        self.subscription_manager
            .get_client_subscriptions(&self.client_id)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::websocket::subscription::SubscriptionManager;
    use std::sync::Arc;
    use tokio::sync::mpsc;

    fn create_test_handler() -> WebSocketHandler {
        let subscription_manager = Arc::new(SubscriptionManager::new());
        let (message_tx, _message_rx) = mpsc::channel(100);
        let client_id = subscription_manager.add_client(message_tx.clone());

        WebSocketHandler::new(
            client_id,
            subscription_manager,
            message_tx,
            None, // No order channel for basic tests
            None, // No cancel order channel for basic tests
        )
    }

    #[tokio::test]
    async fn test_websocket_handler() {
        let subscription_manager = Arc::new(SubscriptionManager::new());
        let (tx, mut rx) = mpsc::channel(100);

        let client_id = subscription_manager.add_client(tx.clone());
        let mut handler =
            WebSocketHandler::new(client_id, subscription_manager.clone(), tx, None, None);

        // 测试订阅消息
        let subscribe_msg = WebSocketMessage::Subscribe {
            subscription: SubscriptionType::OrderBook,
        };
        let json = serde_json::to_string(&subscribe_msg).unwrap();
        let message = Message::Text(json);

        handler.handle_message(message).await.unwrap();

        // 检查是否收到响应
        let response = rx.recv().await.unwrap();
        let parsed: WebSocketMessage = serde_json::from_str(&response).unwrap();

        match parsed {
            WebSocketMessage::Success { message } => {
                assert!(message.contains("Subscribed"));
            }
            _ => panic!("Expected success message"),
        }
    }

    #[test]
    fn test_convert_binance_gtx_order() {
        let handler = create_test_handler();

        let params = BinanceOrderParams {
            api_key: None,
            symbol: "BTCUSDT".to_string(),
            side: "BUY".to_string(),
            order_type: "LIMIT".to_string(),
            position_side: None,
            time_in_force: Some("GTX".to_string()),
            quantity: Some("1.0".to_string()),
            price: Some("50000.0".to_string()),
            new_client_order_id: Some("test_gtx_order".to_string()),
            reduce_only: None,
            stop_price: None,
            working_type: None,
            price_protect: None,
            self_trade_prevention_mode: None,
            timestamp: None,
            signature: None,
            recv_window: None,
        };

        let order = handler.convert_binance_order_to_internal(&params).unwrap();

        assert_eq!(order.symbol, "BTCUSDT");
        assert_eq!(order.side, OrderSide::Buy);
        assert_eq!(order.order_type, OrderType::LimitGTX);
        assert_eq!(order.price, Some(Price::new(50000.0)));
        assert_eq!(order.quantity, 1.0);
        assert_eq!(order.client_order_id, "test_gtx_order");
    }

    #[test]
    fn test_validate_gtx_order_params() {
        let handler = create_test_handler();

        let params = BinanceOrderParams {
            api_key: None,
            symbol: "BTCUSDT".to_string(),
            side: "BUY".to_string(),
            order_type: "LIMIT".to_string(),
            position_side: None,
            time_in_force: Some("GTX".to_string()),
            quantity: Some("1.0".to_string()),
            price: Some("50000.0".to_string()),
            new_client_order_id: Some("test_gtx_order".to_string()),
            reduce_only: None,
            stop_price: None,
            working_type: None,
            price_protect: None,
            self_trade_prevention_mode: None,
            timestamp: None,
            signature: None,
            recv_window: None,
        };

        let result = handler.validate_order_params(&params);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_invalid_time_in_force() {
        let handler = create_test_handler();

        let params = BinanceOrderParams {
            api_key: None,
            symbol: "BTCUSDT".to_string(),
            side: "BUY".to_string(),
            order_type: "LIMIT".to_string(),
            position_side: None,
            time_in_force: Some("INVALID".to_string()),
            quantity: Some("1.0".to_string()),
            price: Some("50000.0".to_string()),
            new_client_order_id: Some("test_order".to_string()),
            reduce_only: None,
            stop_price: None,
            working_type: None,
            price_protect: None,
            self_trade_prevention_mode: None,
            timestamp: None,
            signature: None,
            recv_window: None,
        };

        let result = handler.validate_order_params(&params);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Invalid timeInForce"));
    }
}

#!/usr/bin/env python3
"""
Test script for Binance Order History Downloader

This script tests the downloader with a small dataset to verify functionality.
"""

import os
import sys
import tempfile
import json
from binance_order_history_downloader import BinanceOrderHistoryDownloader


def test_basic_functionality():
    """Test basic functionality of the downloader"""
    print("Testing Binance Order History Downloader...")
    print("=" * 50)
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Using temporary directory: {temp_dir}")
        
        # Initialize downloader
        downloader = BinanceOrderHistoryDownloader()
        
        # Test 1: Check server time
        print("\n1. Testing server time...")
        server_time = downloader.get_server_time()
        if server_time > 0:
            print(f"✓ Server time retrieved: {server_time}")
        else:
            print("✗ Failed to get server time")
            return False
        
        # Test 2: Check symbol info
        print("\n2. Testing symbol validation...")
        symbol_info = downloader.get_symbol_info("BTCUSDT")
        if symbol_info:
            print(f"✓ BTCUSDT found: {symbol_info['status']}")
        else:
            print("✗ Failed to get BTCUSDT info")
            return False
        
        # Test invalid symbol
        invalid_symbol = downloader.get_symbol_info("INVALIDCOIN")
        if invalid_symbol is None:
            print("✓ Invalid symbol correctly rejected")
        else:
            print("✗ Invalid symbol validation failed")
        
        # Test 3: Get order book snapshot
        print("\n3. Testing order book snapshot...")
        order_book = downloader.get_order_book_snapshot("BTCUSDT", limit=10)
        if order_book and 'bids' in order_book and 'asks' in order_book:
            print(f"✓ Order book retrieved: {len(order_book['bids'])} bids, {len(order_book['asks'])} asks")
        else:
            print("✗ Failed to get order book")
            return False
        
        # Test 4: Get recent trades
        print("\n4. Testing recent trades...")
        trades = downloader.get_recent_trades("BTCUSDT", limit=10)
        if trades and len(trades) > 0:
            print(f"✓ Recent trades retrieved: {len(trades)} trades")
        else:
            print("✗ Failed to get recent trades")
            return False
        
        # Test 5: Get klines
        print("\n5. Testing kline data...")
        end_time = server_time
        start_time = end_time - (2 * 60 * 60 * 1000)  # 2 hours ago
        klines = downloader.get_klines("BTCUSDT", "1m", start_time, end_time, limit=10)
        if klines and len(klines) > 0:
            print(f"✓ Kline data retrieved: {len(klines)} candles")
        else:
            print("✗ Failed to get kline data")
            return False
        
        # Test 6: Full download test
        print("\n6. Testing full download...")
        success = downloader.download_order_history("BTCUSDT", 1, temp_dir)
        if success:
            print("✓ Full download completed")
            
            # Check if files were created
            files = os.listdir(temp_dir)
            expected_files = ['orderbook', 'recent_trades', 'klines_1m', 'download_summary']
            found_files = []
            
            for expected in expected_files:
                for file in files:
                    if expected in file and file.endswith('.json'):
                        found_files.append(expected)
                        break
            
            if len(found_files) == len(expected_files):
                print(f"✓ All expected files created: {files}")
                
                # Test file contents
                for file in files:
                    if file.endswith('.json'):
                        file_path = os.path.join(temp_dir, file)
                        try:
                            with open(file_path, 'r') as f:
                                data = json.load(f)
                            print(f"✓ {file} is valid JSON")
                        except Exception as e:
                            print(f"✗ {file} is invalid JSON: {e}")
                            return False
            else:
                print(f"✗ Missing files. Found: {found_files}, Expected: {expected_files}")
                return False
        else:
            print("✗ Full download failed")
            return False
    
    print("\n" + "=" * 50)
    print("✓ All tests passed!")
    return True


def test_error_handling():
    """Test error handling"""
    print("\nTesting error handling...")
    print("-" * 30)
    
    downloader = BinanceOrderHistoryDownloader()
    
    # Test with invalid symbol
    print("Testing invalid symbol...")
    result = downloader.download_order_history("INVALIDCOIN", 1, "/tmp")
    if not result:
        print("✓ Invalid symbol correctly handled")
    else:
        print("✗ Invalid symbol not handled properly")
        return False
    
    print("✓ Error handling tests passed!")
    return True


def main():
    """Run all tests"""
    print("Binance Order History Downloader Test Suite")
    print("=" * 60)
    
    try:
        # Test basic functionality
        if not test_basic_functionality():
            print("\n✗ Basic functionality tests failed!")
            sys.exit(1)
        
        # Test error handling
        if not test_error_handling():
            print("\n✗ Error handling tests failed!")
            sys.exit(1)
        
        print("\n" + "=" * 60)
        print("🎉 All tests passed! The downloader is working correctly.")
        print("\nYou can now use the downloader with confidence:")
        print("python binance_order_history_downloader.py --symbol BTCUSDT --hours 2")
        
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n✗ Unexpected error during testing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

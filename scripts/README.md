# Binance Order History Downloader

这个脚本可以下载指定symbol在过去几个小时的Binance订单历史数据。

## 功能特性

- 下载订单簿快照 (Order Book Snapshot)
- 下载最近交易记录 (Recent Trades)
- 下载K线数据 (1分钟间隔)
- 自动验证交易对是否存在
- 生成下载摘要文件
- 支持自定义时间范围和输出目录

## 安装依赖

```bash
cd scripts
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
# 下载BTCUSDT过去2小时的数据
python binance_order_history_downloader.py --symbol BTCUSDT --hours 2

# 下载ETHUSDT过去6小时的数据
python binance_order_history_downloader.py --symbol ETHUSDT --hours 6

# 指定输出目录
python binance_order_history_downloader.py --symbol BTCUSDT --hours 4 --output-dir ./binance_data
```

### 命令行参数

- `--symbol, -s`: 交易对符号 (必需，例如: BTCUSDT, ETHUSDT)
- `--hours, -h`: 回溯小时数 (默认: 2)
- `--output-dir, -o`: 输出目录 (默认: ./data)
- `--base-url`: Binance API基础URL (默认: https://api.binance.com)

### 输出文件

脚本会在指定目录下创建以下文件：

1. `{SYMBOL}_orderbook_{timestamp}.json` - 订单簿快照
2. `{SYMBOL}_recent_trades_{timestamp}.json` - 最近交易记录
3. `{SYMBOL}_klines_1m_{timestamp}.json` - 1分钟K线数据
4. `{SYMBOL}_download_summary_{timestamp}.json` - 下载摘要

### 数据格式说明

#### 订单簿数据 (Order Book)
```json
{
  "lastUpdateId": 1234567890,
  "bids": [["price", "quantity"], ...],
  "asks": [["price", "quantity"], ...],
  "timestamp": 1640995200000,
  "symbol": "BTCUSDT"
}
```

#### 交易数据 (Trades)
```json
[
  {
    "id": 28457,
    "price": "4.00000100",
    "qty": "12.00000000",
    "quoteQty": "48.000012",
    "time": 1499865549590,
    "isBuyerMaker": true,
    "isBestMatch": true
  }
]
```

#### K线数据 (Klines)
```json
[
  {
    "open_time": 1640995200000,
    "open_price": "46000.00",
    "high_price": "46100.00",
    "low_price": "45900.00",
    "close_price": "46050.00",
    "volume": "123.45",
    "close_time": 1640995259999,
    "quote_asset_volume": "5678901.23",
    "number_of_trades": 100,
    "taker_buy_base_asset_volume": "60.00",
    "taker_buy_quote_asset_volume": "2760000.00"
  }
]
```

## 注意事项

1. **API限制**: 使用的是Binance公开API，有速率限制
2. **数据范围**: 公开API只能获取有限的历史数据
3. **网络要求**: 需要能够访问Binance API (api.binance.com)
4. **时间同步**: 脚本会自动获取Binance服务器时间

## 错误处理

脚本包含完整的错误处理机制：
- 网络连接错误
- 无效的交易对
- API响应错误
- 文件写入错误

## 示例输出

```
Starting download for BTCUSDT - last 2 hours
Output directory: ./data
--------------------------------------------------
Symbol BTCUSDT found. Status: TRADING
Downloading data for BTCUSDT from 2024-01-01 10:00:00 to 2024-01-01 12:00:00
Downloading order book snapshot...
Order book saved to: ./data/BTCUSDT_orderbook_20240101_120000.json
Downloading recent trades...
Recent trades saved to: ./data/BTCUSDT_recent_trades_20240101_120000.json (150 trades)
Downloading 1-minute kline data...
Kline data saved to: ./data/BTCUSDT_klines_1m_20240101_120000.json (120 candles)
Download summary saved to: ./data/BTCUSDT_download_summary_20240101_120000.json
--------------------------------------------------
Download completed successfully!
```

## 扩展功能

如果需要更多功能，可以考虑：
- 添加WebSocket实时数据流
- 支持更多时间间隔的K线数据
- 添加数据压缩和存储优化
- 集成到现有的回测框架中

#!/usr/bin/env python3
"""
Binance Futures Order History Downloader

This script downloads order book data (depth snapshots) and recent trades
for a specified symbol from Binance Futures (perpetual contracts) over the past few hours.

Usage:
    python binance_order_history_downloader.py --symbol BTCUSDT --hours 2
    python binance_order_history_downloader.py --symbol ETHUSDT --hours 6 --output-dir ./data
"""

import argparse
import json
import os
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import requests
from urllib.parse import urlencode


class BinanceFuturesOrderHistoryDownloader:
    """Downloads order book and trade data from Binance Futures API"""

    def __init__(self, base_url: str = "https://fapi.binance.com"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": "BinanceFuturesOrderHistoryDownloader/1.0"})

    def get_server_time(self) -> int:
        """Get Binance Futures server time in milliseconds"""
        try:
            response = self.session.get(f"{self.base_url}/fapi/v1/time")
            response.raise_for_status()
            return response.json()["serverTime"]
        except Exception as e:
            print(f"Error getting server time: {e}")
            return int(time.time() * 1000)

    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """Get futures symbol information to validate if symbol exists"""
        try:
            response = self.session.get(f"{self.base_url}/fapi/v1/exchangeInfo")
            response.raise_for_status()
            data = response.json()

            for sym in data["symbols"]:
                if sym["symbol"] == symbol.upper():
                    return sym
            return None
        except Exception as e:
            print(f"Error getting symbol info: {e}")
            return None

    def get_order_book_snapshot(self, symbol: str, limit: int = 1000) -> Optional[Dict]:
        """Get current order book snapshot"""
        try:
            params = {"symbol": symbol.upper(), "limit": limit}
            response = self.session.get(f"{self.base_url}/fapi/v1/depth", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error getting order book snapshot: {e}")
            return None

    def get_recent_trades(self, symbol: str, limit: int = 1000) -> Optional[List[Dict]]:
        """Get recent trades"""
        try:
            params = {"symbol": symbol.upper(), "limit": limit}
            response = self.session.get(f"{self.base_url}/fapi/v1/trades", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error getting recent trades: {e}")
            return None

    def get_historical_trades(
        self, symbol: str, from_id: Optional[int] = None, limit: int = 1000
    ) -> Optional[List[Dict]]:
        """Get historical trades (requires API key for full access)"""
        try:
            params = {"symbol": symbol.upper(), "limit": limit}
            if from_id:
                params["fromId"] = from_id

            response = self.session.get(f"{self.base_url}/fapi/v1/historicalTrades", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error getting historical trades: {e}")
            return None

    def get_klines(
        self, symbol: str, interval: str, start_time: int, end_time: int, limit: int = 1000
    ) -> Optional[List[List]]:
        """Get kline/candlestick data"""
        try:
            params = {
                "symbol": symbol.upper(),
                "interval": interval,
                "startTime": start_time,
                "endTime": end_time,
                "limit": limit,
            }
            response = self.session.get(f"{self.base_url}/fapi/v1/klines", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error getting klines: {e}")
            return None

    def download_order_history(self, symbol: str, hours: int, output_dir: str = "./data") -> bool:
        """Download order history data for the specified symbol and time period"""

        # Validate symbol
        symbol_info = self.get_symbol_info(symbol)
        if not symbol_info:
            print(f"Error: Symbol {symbol} not found on Binance")
            return False

        print(f"Symbol {symbol} found. Status: {symbol_info['status']}")

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Get current time and calculate start time
        server_time = self.get_server_time()
        end_time = server_time
        start_time = server_time - (hours * 60 * 60 * 1000)  # Convert hours to milliseconds

        timestamp_str = datetime.fromtimestamp(server_time / 1000).strftime("%Y%m%d_%H%M%S")

        print(
            f"Downloading data for {symbol} from {datetime.fromtimestamp(start_time / 1000)} to {datetime.fromtimestamp(end_time / 1000)}"
        )

        # 1. Get current order book snapshot
        print("Downloading order book snapshot...")
        order_book = self.get_order_book_snapshot(symbol)
        if order_book:
            order_book["timestamp"] = server_time
            order_book["symbol"] = symbol

            filename = os.path.join(output_dir, f"{symbol}_orderbook_{timestamp_str}.json")
            with open(filename, "w") as f:
                json.dump(order_book, f, indent=2)
            print(f"Order book saved to: {filename}")

        # 2. Get recent trades
        print("Downloading recent trades...")
        recent_trades = self.get_recent_trades(symbol)
        if recent_trades:
            # Filter trades within the time window
            filtered_trades = []
            for trade in recent_trades:
                if trade["time"] >= start_time:
                    filtered_trades.append(trade)

            if filtered_trades:
                filename = os.path.join(output_dir, f"{symbol}_recent_trades_{timestamp_str}.json")
                with open(filename, "w") as f:
                    json.dump(filtered_trades, f, indent=2)
                print(f"Recent trades saved to: {filename} ({len(filtered_trades)} trades)")
            else:
                print("No recent trades found in the specified time window")

        # 3. Get kline data (1-minute intervals)
        print("Downloading 1-minute kline data...")
        klines = self.get_klines(symbol, "1m", start_time, end_time)
        if klines:
            # Convert kline data to more readable format
            formatted_klines = []
            for kline in klines:
                formatted_klines.append(
                    {
                        "open_time": kline[0],
                        "open_price": kline[1],
                        "high_price": kline[2],
                        "low_price": kline[3],
                        "close_price": kline[4],
                        "volume": kline[5],
                        "close_time": kline[6],
                        "quote_asset_volume": kline[7],
                        "number_of_trades": kline[8],
                        "taker_buy_base_asset_volume": kline[9],
                        "taker_buy_quote_asset_volume": kline[10],
                    }
                )

            filename = os.path.join(output_dir, f"{symbol}_klines_1m_{timestamp_str}.json")
            with open(filename, "w") as f:
                json.dump(formatted_klines, f, indent=2)
            print(f"Kline data saved to: {filename} ({len(formatted_klines)} candles)")

        # 4. Create summary file
        summary = {
            "symbol": symbol,
            "download_time": datetime.fromtimestamp(server_time / 1000).isoformat(),
            "time_range": {
                "start": datetime.fromtimestamp(start_time / 1000).isoformat(),
                "end": datetime.fromtimestamp(end_time / 1000).isoformat(),
                "hours": hours,
            },
            "files_created": [
                f"{symbol}_orderbook_{timestamp_str}.json",
                f"{symbol}_recent_trades_{timestamp_str}.json",
                f"{symbol}_klines_1m_{timestamp_str}.json",
            ],
            "symbol_info": symbol_info,
        }

        summary_filename = os.path.join(output_dir, f"{symbol}_download_summary_{timestamp_str}.json")
        with open(summary_filename, "w") as f:
            json.dump(summary, f, indent=2)
        print(f"Download summary saved to: {summary_filename}")

        return True


def main():
    parser = argparse.ArgumentParser(description="Download Binance order history data")
    parser.add_argument("--symbol", "-s", required=True, help="Trading symbol (e.g., BTCUSDT)")
    parser.add_argument("--hours", "-h", type=int, default=2, help="Number of hours to look back (default: 2)")
    parser.add_argument("--output-dir", "-o", default="./data", help="Output directory (default: ./data)")
    parser.add_argument("--base-url", default="https://api.binance.com", help="Binance API base URL")

    args = parser.parse_args()

    if args.hours <= 0:
        print("Error: Hours must be a positive number")
        sys.exit(1)

    if args.hours > 24:
        print("Warning: Requesting more than 24 hours of data. Some data might not be available through public API.")

    downloader = BinanceOrderHistoryDownloader(args.base_url)

    print(f"Starting download for {args.symbol} - last {args.hours} hours")
    print(f"Output directory: {args.output_dir}")
    print("-" * 50)

    success = downloader.download_order_history(args.symbol, args.hours, args.output_dir)

    if success:
        print("-" * 50)
        print("Download completed successfully!")
    else:
        print("-" * 50)
        print("Download failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()

#!/bin/bash

# Quick download script for common use cases

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Binance Order History Quick Download"
echo "===================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is required but not installed."
    exit 1
fi

# Check if requests is installed
if ! python3 -c "import requests" &> /dev/null; then
    echo "Installing required dependencies..."
    pip3 install -r requirements.txt
fi

# Default values
SYMBOL=""
HOURS=2
OUTPUT_DIR="../data"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--symbol)
            SYMBOL="$2"
            shift 2
            ;;
        -h|--hours)
            HOURS="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -s, --symbol SYMBOL     Trading symbol (e.g., BTCUSDT)"
            echo "  -h, --hours HOURS       Number of hours to look back (default: 2)"
            echo "  -o, --output-dir DIR    Output directory (default: ../data)"
            echo "  --help                  Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 -s BTCUSDT -h 4"
            echo "  $0 -s ETHUSDT -h 6 -o ./my_data"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Interactive mode if no symbol provided
if [ -z "$SYMBOL" ]; then
    echo ""
    echo "Popular trading pairs:"
    echo "  1) BTCUSDT"
    echo "  2) ETHUSDT"
    echo "  3) BNBUSDT"
    echo "  4) ADAUSDT"
    echo "  5) SOLUSDT"
    echo ""
    read -p "Enter trading symbol (or choose 1-5): " choice
    
    case $choice in
        1) SYMBOL="BTCUSDT" ;;
        2) SYMBOL="ETHUSDT" ;;
        3) SYMBOL="BNBUSDT" ;;
        4) SYMBOL="ADAUSDT" ;;
        5) SYMBOL="SOLUSDT" ;;
        *) SYMBOL="$choice" ;;
    esac
fi

if [ -z "$SYMBOL" ]; then
    echo "Error: Symbol is required"
    exit 1
fi

# Validate hours
if ! [[ "$HOURS" =~ ^[0-9]+$ ]] || [ "$HOURS" -le 0 ]; then
    echo "Error: Hours must be a positive integer"
    exit 1
fi

echo ""
echo "Configuration:"
echo "  Symbol: $SYMBOL"
echo "  Hours: $HOURS"
echo "  Output: $OUTPUT_DIR"
echo ""

read -p "Proceed with download? (y/N): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "Download cancelled."
    exit 0
fi

echo ""
echo "Starting download..."
python3 binance_order_history_downloader.py --symbol "$SYMBOL" --hours "$HOURS" --output-dir "$OUTPUT_DIR"

echo ""
echo "Download completed! Files saved to: $OUTPUT_DIR"
echo "You can view the downloaded files:"
echo "  ls -la $OUTPUT_DIR/${SYMBOL}_*"
